# 新的Token机制说明 (V2.0)

## 概述

为了解决之前token机制中每次API请求都会触发token刷新导致的性能问题，我们实现了一个全新的**服务端管理的token机制**。

## 主要改进

### 1. 服务端Session管理
- 新增 `user_sessions` 表来存储用户会话信息
- Token由服务端生成和管理，不再使用JWT
- 每次API请求时自动延长session过期时间

### 2. 前端简化
- 移除了前端的自动token刷新逻辑
- 不再需要频繁的refreshtoken请求
- 只在token真正过期时才会收到401错误

### 3. 性能优化
- 大幅减少了不必要的token刷新请求
- 提高了系统整体性能
- 减少了服务器负载

## 技术实现

### 数据库结构

新增 `user_sessions` 表：

```sql
CREATE TABLE user_sessions (
    id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    token VARCHAR UNIQUE NOT NULL,
    created_at DATETIME NOT NULL,
    last_activity DATETIME NOT NULL,
    expires_at DATETIME NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    user_agent TEXT,
    PRIMARY KEY (id),
    FOREIGN KEY(user_id) REFERENCES users (id)
);
```

### 核心组件

#### 1. SessionManager (服务端)
- `create_session()`: 创建新的用户session
- `validate_and_extend_session()`: 验证token并自动延长session
- `invalidate_session()`: 使session失效（登出）
- `cleanup_expired_sessions()`: 清理过期session

#### 2. 认证依赖 (FastAPI)
- `get_current_user()`: 新的基于session的用户认证
- `get_current_user_jwt()`: 保留的JWT认证（兼容性）

#### 3. 前端API (Vue.js)
- 简化的HTTP拦截器，移除自动刷新逻辑
- 新增logout API调用

## 配置参数

### SessionManager配置
```python
SESSION_EXPIRE_HOURS = 24      # Session过期时间（小时）
AUTO_EXTEND_MINUTES = 30       # 每次请求自动延长时间（分钟）
```

### 清理任务配置
```python
cleanup_interval_hours = 1     # 清理过期session的间隔（小时）
```

## 使用流程

### 1. 用户登录
```python
# 后端
token = SessionManager.create_session(db, user.id, user_agent)
return {"access_token": token, "token_type": "bearer"}
```

### 2. API请求认证
```python
# 每次API请求时
user = SessionManager.validate_and_extend_session(db, token)
# 自动延长session过期时间
```

### 3. 用户登出
```python
# 后端
SessionManager.invalidate_session(db, token)

# 前端
await authApi.logout()
userStore.logout()
```

## 迁移步骤

### 1. 数据库迁移
```bash
python database/migrate_to_v0.0.2.py
```

### 2. 重启服务
重启后端API服务以加载新的认证逻辑

### 3. 清除旧Token
用户需要重新登录以获取新的session token

## 兼容性

- 保留了JWT认证函数 `get_current_user_jwt()` 以确保向后兼容
- 现有的API端点无需修改，只需更新依赖注入
- 前端代码向后兼容，只是移除了自动刷新逻辑

## 监控和维护

### 1. Session清理
- 定时任务自动清理过期session
- 可手动运行清理脚本：`python api/app/tasks/session_cleanup.py`

### 2. 日志监控
- 所有session操作都有详细日志记录
- 可通过日志监控用户登录/登出行为

### 3. 性能监控
- 监控session表大小
- 定期检查清理任务执行情况

## 安全考虑

### 1. Token安全
- 使用 `secrets.token_urlsafe(32)` 生成安全的随机token
- Token存储在数据库中，支持即时撤销

### 2. Session管理
- 支持单用户多session（多设备登录）
- 可强制使用户所有session失效

### 3. 自动过期
- Session有明确的过期时间
- 过期session自动失效，无法继续使用

## 故障排除

### 常见问题

1. **用户频繁需要重新登录**
   - 检查 `SESSION_EXPIRE_HOURS` 配置
   - 确认清理任务没有过于频繁执行

2. **Session表增长过快**
   - 检查清理任务是否正常运行
   - 考虑调整清理间隔

3. **性能问题**
   - 检查session表索引是否正确创建
   - 监控数据库查询性能

### 调试命令

```python
# 查看用户活跃session
sessions = SessionManager.get_user_sessions(db, user_id)

# 手动清理过期session
count = SessionManager.cleanup_expired_sessions(db)

# 使用户所有session失效
count = SessionManager.invalidate_user_sessions(db, user_id)
```
