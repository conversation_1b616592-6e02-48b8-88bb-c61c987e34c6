"""
服务端Session管理模块
用于管理用户token的生命周期，实现每次API请求自动续期机制
"""
import secrets
import logging
from datetime import datetime, timedelta
from typing import Optional
from sqlalchemy.orm import Session
from app.models.user import User, UserSession
from app.core.database import get_db

logger = logging.getLogger(__name__)

class SessionManager:
    """服务端Session管理器"""
    
    # Session配置
    SESSION_EXPIRE_HOURS = 24  # Session过期时间（小时）
    AUTO_EXTEND_MINUTES = 30   # 每次请求自动延长时间（分钟）
    
    @staticmethod
    def generate_token() -> str:
        """生成安全的随机token"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def create_session(db: Session, user_id: int, user_agent: str = None) -> str:
        """
        创建新的用户session
        
        Args:
            db: 数据库session
            user_id: 用户ID
            user_agent: 用户代理字符串
            
        Returns:
            str: 生成的token
        """
        # 生成新token
        token = SessionManager.generate_token()
        
        # 计算过期时间
        expires_at = datetime.utcnow() + timedelta(hours=SessionManager.SESSION_EXPIRE_HOURS)
        
        # 创建session记录
        session = UserSession(
            user_id=user_id,
            token=token,
            expires_at=expires_at,
            user_agent=user_agent
        )
        
        db.add(session)
        db.commit()
        
        logger.info(f"为用户 {user_id} 创建新session，token: {token[:10]}..., 过期时间: {expires_at}")
        return token
    
    @staticmethod
    def validate_and_extend_session(db: Session, token: str) -> Optional[User]:
        """
        验证token并自动延长session
        
        Args:
            db: 数据库session
            token: 用户token
            
        Returns:
            User: 如果token有效返回用户对象，否则返回None
        """
        # 查找session
        session = db.query(UserSession).filter(
            UserSession.token == token,
            UserSession.is_active == True
        ).first()
        
        if not session:
            logger.warning(f"未找到token对应的session: {token[:10]}...")
            return None
        
        # 检查是否过期
        now = datetime.utcnow()
        if now > session.expires_at:
            logger.warning(f"Session已过期: {token[:10]}..., 过期时间: {session.expires_at}")
            # 标记session为非活跃状态
            session.is_active = False
            db.commit()
            return None
        
        # 获取用户信息
        user = db.query(User).filter(User.id == session.user_id).first()
        if not user:
            logger.error(f"Session对应的用户不存在: user_id={session.user_id}")
            return None
        
        # 自动延长session
        session.last_activity = now
        session.expires_at = now + timedelta(minutes=SessionManager.AUTO_EXTEND_MINUTES)
        db.commit()
        
        logger.debug(f"Session自动延长: {token[:10]}..., 新过期时间: {session.expires_at}")
        return user
    
    @staticmethod
    def invalidate_session(db: Session, token: str) -> bool:
        """
        使session失效（用户登出）
        
        Args:
            db: 数据库session
            token: 用户token
            
        Returns:
            bool: 是否成功使session失效
        """
        session = db.query(UserSession).filter(
            UserSession.token == token,
            UserSession.is_active == True
        ).first()
        
        if session:
            session.is_active = False
            db.commit()
            logger.info(f"Session已失效: {token[:10]}...")
            return True
        
        return False
    
    @staticmethod
    def invalidate_user_sessions(db: Session, user_id: int) -> int:
        """
        使用户的所有session失效
        
        Args:
            db: 数据库session
            user_id: 用户ID
            
        Returns:
            int: 失效的session数量
        """
        count = db.query(UserSession).filter(
            UserSession.user_id == user_id,
            UserSession.is_active == True
        ).update({"is_active": False})
        
        db.commit()
        logger.info(f"用户 {user_id} 的 {count} 个session已失效")
        return count
    
    @staticmethod
    def cleanup_expired_sessions(db: Session) -> int:
        """
        清理过期的session记录
        
        Args:
            db: 数据库session
            
        Returns:
            int: 清理的session数量
        """
        now = datetime.utcnow()
        count = db.query(UserSession).filter(
            UserSession.expires_at < now
        ).update({"is_active": False})
        
        db.commit()
        logger.info(f"清理了 {count} 个过期session")
        return count
    
    @staticmethod
    def get_user_sessions(db: Session, user_id: int) -> list:
        """
        获取用户的活跃session列表
        
        Args:
            db: 数据库session
            user_id: 用户ID
            
        Returns:
            list: session列表
        """
        sessions = db.query(UserSession).filter(
            UserSession.user_id == user_id,
            UserSession.is_active == True,
            UserSession.expires_at > datetime.utcnow()
        ).all()
        
        return sessions
