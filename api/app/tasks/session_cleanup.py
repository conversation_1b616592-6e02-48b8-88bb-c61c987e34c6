"""
Session清理定时任务
定期清理过期的用户session记录
"""

import logging
import asyncio
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.core.session_manager import SessionManager

logger = logging.getLogger(__name__)

class SessionCleanupTask:
    """Session清理任务"""
    
    def __init__(self, cleanup_interval_hours: int = 1):
        """
        初始化清理任务
        
        Args:
            cleanup_interval_hours: 清理间隔（小时）
        """
        self.cleanup_interval_hours = cleanup_interval_hours
        self.is_running = False
    
    async def cleanup_expired_sessions(self):
        """清理过期的session"""
        db = SessionLocal()
        try:
            count = SessionManager.cleanup_expired_sessions(db)
            logger.info(f"清理了 {count} 个过期session")
            return count
        except Exception as e:
            logger.error(f"清理过期session时发生错误: {e}")
            return 0
        finally:
            db.close()
    
    async def start_cleanup_task(self):
        """启动定时清理任务"""
        if self.is_running:
            logger.warning("清理任务已在运行中")
            return
        
        self.is_running = True
        logger.info(f"启动session清理任务，间隔: {self.cleanup_interval_hours}小时")
        
        try:
            while self.is_running:
                await self.cleanup_expired_sessions()
                
                # 等待下次清理
                await asyncio.sleep(self.cleanup_interval_hours * 3600)
                
        except asyncio.CancelledError:
            logger.info("清理任务被取消")
        except Exception as e:
            logger.error(f"清理任务发生错误: {e}")
        finally:
            self.is_running = False
            logger.info("清理任务已停止")
    
    def stop_cleanup_task(self):
        """停止清理任务"""
        self.is_running = False
        logger.info("正在停止清理任务...")

# 全局清理任务实例
cleanup_task = SessionCleanupTask()

async def start_session_cleanup():
    """启动session清理任务"""
    await cleanup_task.start_cleanup_task()

def stop_session_cleanup():
    """停止session清理任务"""
    cleanup_task.stop_cleanup_task()

if __name__ == "__main__":
    # 直接运行清理任务
    import asyncio
    
    async def run_once():
        """运行一次清理任务"""
        task = SessionCleanupTask()
        count = await task.cleanup_expired_sessions()
        print(f"清理了 {count} 个过期session")
    
    asyncio.run(run_once())
