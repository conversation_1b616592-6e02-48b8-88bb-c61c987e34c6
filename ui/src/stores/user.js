import { defineStore } from 'pinia';
import { authApi } from '../api/auth';

export const useUserStore = defineStore('user', {
  state: () => ({
    // Initialize with data from localStorage or defaults
    token: localStorage.getItem('token') || null,
    userInfo: JSON.parse(localStorage.getItem('userInfo')) || {
      name: 'Guest',
      roles: [],
      is_admin: false,
      // Add other user-specific properties here as needed
    },
  }),
  getters: {
    isLoggedIn: (state) => !!state.token,
    userName: (state) => state.userInfo.name,
    userRoles: (state) => state.userInfo.roles || [],
    isAdmin: (state) => state.userInfo.is_admin || false,
    // Add other getters as needed
  },
  actions: {
    login(userData, token) {
      this.token = token;
      this.userInfo = userData;
      localStorage.setItem('token', token);
      localStorage.setItem('userInfo', JSON.stringify(userData));
      // Potentially redirect or perform other actions after login
    },
    async logout() {
      try {
        // 调用后端登出API使session失效
        if (this.token) {
          await authApi.logout();
        }
      } catch (error) {
        console.warn('登出API调用失败，但仍会清除本地数据:', error);
      } finally {
        // 无论API调用是否成功，都清除本地数据
        this.token = null;
        this.userInfo = {
          name: 'Guest',
          roles: [],
          is_admin: false,
        };
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        // Potentially redirect to login page
        // router.push('/login'); // If router is accessible here or handle in component
      }
    },
    loadUserFromLocalStorage() {
      const token = localStorage.getItem('token');
      const userInfo = localStorage.getItem('userInfo');
      if (token && userInfo) {
        this.token = token;
        this.userInfo = JSON.parse(userInfo);
      }
    },
    // Add other actions like fetching user profile, updating settings etc.
  },
}); 