import { http } from '../utils/http'

export const authApi = {
  // 用户登录
  login(data) {
    return http.post('/login', data)
  },

  // 获取用户信息
  getUserInfo() {
    return http.get('/users/me')
  },

  // 用户登出
  logout() {
    return http.post('/logout')
  },

  // 修改密码
  changePassword(data) {
    return http.post('/change-password', data)
  },

  // 刷新token（保留兼容性，但不再推荐使用）
  refreshToken() {
    return http.post('/refresh-token')
  }
}
