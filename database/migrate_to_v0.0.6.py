#!/usr/bin/env python3
"""
数据库迁移脚本 - 升级到 V0.0.6
1. 移除requirement_code字段的唯一约束并允许为null
2. 添加用户会话表以支持服务端token管理
"""

import sqlite3
import os
import sys
from datetime import datetime

def get_db_path():
    """获取数据库文件路径"""
    # 优先使用API目录下的数据库文件，因为这是实际使用的数据库
    possible_paths = [
        "api/site.db",  # api目录（优先）
        "../api/site.db",  # 从database目录访问api目录
        "site.db",  # 当前目录
        "../site.db",  # 上级目录
        "../../site.db",  # 上两级目录
    ]

    for path in possible_paths:
        if os.path.exists(path):
            print(f"找到数据库文件: {path}")
            return path

    # 如果都找不到，使用API目录的默认路径
    return "api/site.db"

def check_table_exists(cursor, table_name):
    """检查表是否存在"""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
    """, (table_name,))
    return cursor.fetchone() is not None

def execute_sql_file(cursor, sql_file_path):
    """执行SQL文件"""
    if not os.path.exists(sql_file_path):
        print(f"❌ SQL文件不存在: {sql_file_path}")
        return False

    try:
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()

        # 分割SQL语句并执行
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]

        for stmt in sql_statements:
            if stmt:
                cursor.execute(stmt)

        print(f"✅ 成功执行SQL文件: {sql_file_path}")
        return True

    except Exception as e:
        print(f"❌ 执行SQL文件失败 {sql_file_path}: {e}")
        return False

def migrate_database():
    """执行数据库迁移"""
    db_path = get_db_path()
    print(f"使用数据库文件: {db_path}")

    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 按顺序执行迁移脚本
        migration_scripts = [
            "V0.0.6/migrate_requirement_code.sql",
            "V0.0.6/add_user_sessions_table.sql"
        ]

        success_count = 0

        for script in migration_scripts:
            script_path = os.path.join(os.path.dirname(__file__), script)
            print(f"\n执行迁移脚本: {script}")

            # 对于requirement_code迁移，检查是否需要执行
            if "migrate_requirement_code" in script:
                # 检查requirements表是否存在唯一约束
                cursor.execute("""
                    SELECT sql FROM sqlite_master
                    WHERE type='table' AND name='requirements'
                """)
                result = cursor.fetchone()
                if result and 'UNIQUE (requirement_code)' not in result[0]:
                    print("requirement_code唯一约束已移除，跳过此脚本")
                    success_count += 1
                    continue

            # 对于user_sessions表，检查是否已存在
            if "add_user_sessions_table" in script:
                if check_table_exists(cursor, 'user_sessions'):
                    print("user_sessions表已存在，跳过此脚本")
                    success_count += 1
                    continue

            # 执行SQL脚本
            if execute_sql_file(cursor, script_path):
                success_count += 1
            else:
                print(f"❌ 脚本执行失败: {script}")
                return False

        # 提交所有更改
        conn.commit()

        if success_count == len(migration_scripts):
            print(f"\n✅ 所有迁移脚本执行完成 ({success_count}/{len(migration_scripts)})")
            return True
        else:
            print(f"\n❌ 部分迁移脚本执行失败 ({success_count}/{len(migration_scripts)})")
            return False

    except sqlite3.Error as e:
        print(f"❌ 数据库迁移失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 迁移过程中发生错误: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("数据库迁移脚本 - V0.0.6")
    print("1. 移除requirement_code字段的唯一约束并允许为null")
    print("2. 添加用户会话表以支持服务端token管理")
    print("=" * 60)

    success = migrate_database()

    if success:
        print("\n🎉 迁移成功完成!")
        print("✅ requirement_code字段约束已更新")
        print("✅ 用户会话表已创建")
        print("现在可以使用新的服务端token管理功能")
        sys.exit(0)
    else:
        print("\n💥 迁移失败!")
        print("请检查错误信息并重试")
        sys.exit(1)

if __name__ == "__main__":
    main()
