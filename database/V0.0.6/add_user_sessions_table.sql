-- 添加用户会话表，用于服务端token管理
-- 版本: V0.0.6
-- 创建时间: 2024-01-01

-- 创建用户会话表
CREATE TABLE user_sessions (
    id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    token VARCHAR UNIQUE NOT NULL,
    created_at DATETIME NOT NULL,
    last_activity DATETIME NOT NULL,
    expires_at DATETIME NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    user_agent TEXT,
    PRIMARY KEY (id),
    FOREIGN KEY(user_id) REFERENCES users (id)
);

-- 创建索引
CREATE INDEX ix_user_sessions_id ON user_sessions (id);
CREATE INDEX ix_user_sessions_user_id ON user_sessions (user_id);
CREATE UNIQUE INDEX ix_user_sessions_token ON user_sessions (token);
CREATE INDEX ix_user_sessions_expires_at ON user_sessions (expires_at);
CREATE INDEX ix_user_sessions_is_active ON user_sessions (is_active);
